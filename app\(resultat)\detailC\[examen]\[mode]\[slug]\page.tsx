import { EEPayload, EOPayload, ResultatEE } from '@/types';

import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import DetailHeaderE from '@/components/DetailHeaderE';
import SERVER_API from '@/lib/axios.server';
import { EOCardTCF, EOCardTEF } from '@/components/EOCard';
import { EECardTCF, EECardTEF } from '@/components/EECard';
import logger from '@/lib/logger';

export const metadata: Metadata = {
  title: 'TCF | Detail',
  description:
    "Application web pour s'exercer au test de connaissance du français",
};

type partialMode = 'EE' | 'EO';
type Examen = 'TEF' | 'TCF';

const fetchDetail = async (
  id: string,
  mode: partialMode,
  examen: Examen,
): Promise<ResultatEE<SerieTEF> | ResultatEE | null> => {
  let urlpath = '';
  if (mode == 'EE') urlpath = 'eeTest/tests';
  if (mode == 'EO') urlpath = 'eoTest/tests';
  try {
    const { data } = await SERVER_API.get<ResultatEE<SerieTEF> | ResultatEE>(
      `/api/${examen == 'TCF' ? '' : 'tef/'}${urlpath}/${id}?populateSerie=false`,
    );
    return data;
  } catch (error) {
    return null;
  }
};

export default async function Page({
  params,
}: {
  params: {
    examen: Examen;
    slug: string;
    mode: partialMode;
  };
}) {
  const detailSet = await fetchDetail(params.slug, params.mode, params.examen);
  if (!detailSet) {
    notFound();
  }
  const payload = JSON.parse(detailSet.payload);
  logger.log('result', detailSet.resultat);
  
  return (
    <div
      className="flex w-[95vw] flex-col gap-5 px-2 py-10 md:w-full lg:px-28"
      suppressHydrationWarning
    >
      {params.examen == 'TCF' ? (
        <>
          <DetailHeaderE
            resultat={detailSet as ResultatEE}
            mode={params.mode}
          />
          {params.mode === 'EE'
            ? (detailSet as ResultatEE).serie.eeQuestions
                .at(0)
                ?.tasks.map((task) => (
                  <EECardTCF
                    detailSet={detailSet as ResultatEE}
                    key={task._id}
                    payload={payload as EEPayload}
                    task={task}
                  />
                ))
            : (detailSet as ResultatEE).serie.eoQuestions
                .at(0)
                ?.tasks.map((task) => (
                  <EOCardTCF
                    detailSet={detailSet as ResultatEE}
                    key={task._id}
                    payload={payload as EOPayload}
                    task={task}
                  />
                ))}
        </>
      ) : (
        <>
          <DetailHeaderE
            resultat={detailSet as ResultatEE}
            mode={params.mode}
          />
          {params.mode === 'EE'
            ? (detailSet as ResultatEE<SerieTEF>).serie.eeQuestions
                .at(0)
                ?.sections.map((task) => (
                  <EECardTEF
                    detailSet={detailSet as ResultatEE}
                    key={task._id}
                    payload={payload as EEPayload}
                    section={task}
                  />
                ))
            : (detailSet as ResultatEE<SerieTEF>).serie.eoQuestions
                .at(0)
                ?.sections.map((task) => (
                  <EOCardTEF
                    detailSet={detailSet as any}
                    key={task._id}
                    payload={payload}
                    section={task}
                  />
                ))}
        </>
      )}
    </div>
  );
}
