import { fechSerie } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { isTestAvailable } from '@/lib/utils';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import TestEOTCF from '../../../_components/testEO';
import EOProvider from '@/context/ee-provider';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  if (!session)
    redirect(`/auth?callbackUrl=/examen/tcf/${slug}/expression-orale`);
  const serie = await fechSerie(slug, session.user.accessToken, 'EO');
  if (!serie) return notFound();

  if (!isTestAvailable('EE', serie)) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="mt-5 w-full">
      <EOProvider initialCurrent={1}>
        <TestEOTCF serie={serie} session={session} />
      </EOProvider>
    </section>
  );
}

export default Page;
