import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ valid: false }, { status: 400 });
    }

    // <PERSON><PERSON> le token auprès de votre API backend
    const BaseURL =
      process.env.NEXT_PUBLIC_BASEURL_PROD ||
      process.env.NEXT_PUBLIC_BASEURL ||
      '';

    const response = await fetch(`${BaseURL}/api/user/users/user-info`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return NextResponse.json(
      {
        valid: response.ok,
      },
      {
        status: response.ok ? 200 : 401,
      },
    );
  } catch (error) {
    return NextResponse.json(
      {
        valid: false,
        error: 'Token validation failed',
      },
      {
        status: 500,
      },
    );
  }
}
