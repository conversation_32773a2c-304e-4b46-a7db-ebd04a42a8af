'use client';
import Link from 'next/link';
import { Lock } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

export const MethodologieBtn = ({
  type,
  CanUse,
}: {
  type: string;
  CanUse: boolean;
}) => {
  return (
    <>
      {CanUse ? (
        <Link href={`/methodologie/${type}`} id="other">
          <button className="relative h-[80px] w-full rounded-sm border bg-blue-400 text-center">
            <div className="flex flex-col gap-1">
              <p className="text-3xl font-semibold">Méthodologie {type}</p>
            </div>
          </button>
        </Link>
      ) : (
        <Link href={'/checkout'} className="cursor-pointer" id="other">
          <button
            type="button"
            className="relative h-[80px] w-full rounded-sm border bg-zinc-300 text-center"
          >
            <p className="text-3xl font-semibold"> Méthodologie {type}</p>
            <div className="absolute right-3 top-2 h-fit w-fit">
              <Lock className="h-6 w-6 font-bold text-gray-800" />
            </div>
          </button>
        </Link>
      )}
    </>
  );
};

export const SerieBtn = ({
  type,
  CanUse,
  lib,
  session,
}: {
  type: string;
  CanUse: boolean;
  lib: string;
  session?: string;
}) => {
  return (
    <>
      {CanUse ? (
        <Link href={`/examen/${type}/${lib}`} id="other" className="relative">
          <button className="relative h-[80px] w-full rounded-sm border bg-blue-400 text-center">
            <p className="text-3xl font-semibold">Série {lib}</p>
          </button>
          {session && (
            <div className="absolute -bottom-2 flex w-full justify-center">
              <span className="rotate-card w-fit border bg-white px-3 py-1 text-blue-500">
                <p className="text-center text-xs font-bold">
                  Session {session}
                </p>
              </span>
            </div>
          )}
        </Link>
      ) : (
        <Link href={'/checkout'} className="relative cursor-pointer" id="other">
          <button
            type="button"
            className="relative h-[80px] w-full rounded-sm border bg-zinc-300 text-center"
          >
            <p className="text-3xl font-semibold"> Série {lib}</p>
            <div className="absolute right-3 top-2 h-fit w-fit">
              <Lock className="h-6 w-6 font-bold text-gray-800" />
            </div>
          </button>
          {session && (
            <div className="absolute -bottom-2 flex w-full justify-center">
              <span className="rotate-card w-fit bg-primary px-3 py-1 text-white">
                <p className="text-center text-xs font-bold">
                  Session {session}
                </p>
              </span>
            </div>
          )}
        </Link>
      )}
    </>
  );
};

interface EndTextBtnProps {
  children: React.ReactNode;
  onClick: () => void;
}
export const EndTextBtn = ({ children, onClick }: EndTextBtnProps) => {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="text-center uppercase">
            Voulez-vous terminer votre test?
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="w-full space-y-3 text-center font-semibold">
              <p className="">
                {' '}
                En confirmant vous mettez fin au test et décidez d’envoyer vos
                propositions de réponses. Cette action est irréversible!
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Non</AlertDialogCancel>
          <AlertDialogAction onClick={() => onClick()}>Oui</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
