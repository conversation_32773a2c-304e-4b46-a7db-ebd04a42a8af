import { chain } from './middlewares/chain';
import { withAuth } from './middlewares/withAuth';
import { withMaintenance } from './middlewares/withMaintenance';
import { withPlan } from './middlewares/withPlan';
import { verifyValidToken } from './middlewares/verifyValidToken';

const middlewares = [verifyValidToken, withAuth, withPlan, withMaintenance];
export default chain(middlewares);

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)',
  ],
};
