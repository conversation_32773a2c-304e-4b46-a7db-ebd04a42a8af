'use client';
import React, { useEffect, useState } from 'react';
import { getTestSet } from '@/lib/utils';

import { GLOBAL_EE_TEST_TIME } from '@/config';
import { Serie } from '@/types';
import GlobalTimer from './timer';
import { EndTextBtn } from '../../_components/button';
import { toast as sonner } from 'sonner';
import { useEEState } from '@/context/tcf';
import { useRouter } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Task from '../../_components/task';
import convert from '@/lib/helpers';
import { saveTestEEUseCase } from '../actions';
import useCredits from '@/hooks/use-credits';
import { useSession } from 'next-auth/react';
import CorrectionModeSelector from '../../_components/correction-mode-selector';

export default function TestEETCF({ serie }: { serie: Serie }) {
  const {
    textOne,
    textThree,
    textTwo,
    resetEE,
    setTextOne,
    setTextThree,
    setTextTwo,
    setLocalVariables,
  } = useEEState();
  const { checkCredit } = useCredits();
  const { data: session, update } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [starting, setstarting] = useState<boolean>(true);
  const [showCorrectionSelector, setShowCorrectionSelector] = useState(false);
  const [pendingSubmission, setPendingSubmission] = useState<any>(null);
  const [LOCAL_TEXT_1, setLOCAL_TEXT_1] = useState<string>();
  const [LOCAL_TEXT_2, setLOCAL_TEXT_2] = useState<string>();
  const [LOCAL_TEXT_3, setLOCAL_TEXT_3] = useState<string>();
  const [TimeName, setTimeName] = useState<string>();
  const testSet = getTestSet(serie);
  const taskOne = testSet?.EE?.[0].tasks.find((t) => t.numero == 79)!;
  const taskTwo = testSet?.EE?.[0].tasks.find((t) => t.numero == 80)!;
  const taskThree = testSet?.EE?.[0].tasks.find((t) => t.numero == 81)!;

  useEffect(() => {
    if (session && serie) {
      setLOCAL_TEXT_1(`EE_RES_${serie.libelle}_TASK_1_${session.user._id}`);
      setLOCAL_TEXT_2(`EE_RES_${serie.libelle}_TASK_2_${session.user._id}`);
      setLOCAL_TEXT_3(`EE_RES_${serie.libelle}_TASK_3_${session.user._id}`);
      setTimeName(`EE_TIME_${serie.libelle}_${session.user._id}`);
    }
  }, [session, serie]);

  const clearLocalStorage = async () => {
    if (TimeName) localStorage.removeItem(TimeName);
    if (LOCAL_TEXT_1) localStorage.removeItem(LOCAL_TEXT_1);
    if (LOCAL_TEXT_2) localStorage.removeItem(LOCAL_TEXT_2);
    if (LOCAL_TEXT_3) localStorage.removeItem(LOCAL_TEXT_3);
  };

  useEffect(() => {
    if (!LOCAL_TEXT_1 || !LOCAL_TEXT_2 || !LOCAL_TEXT_3) return;
    setLocalVariables({ LOCAL_TEXT_1, LOCAL_TEXT_2, LOCAL_TEXT_3 });
    const prevTextOne = JSON.parse(localStorage.getItem(LOCAL_TEXT_1) ?? '{}')
      .text as string;
    const prevTextTwo = JSON.parse(localStorage.getItem(LOCAL_TEXT_2) ?? '{}')
      .text as string;
    const prevTextThree = JSON.parse(localStorage.getItem(LOCAL_TEXT_3) ?? '{}')
      .text as string;
    if (prevTextOne) setTextOne(prevTextOne);
    if (prevTextTwo) setTextTwo(prevTextTwo);
    if (prevTextThree) setTextThree(prevTextThree);
  }, [
    LOCAL_TEXT_1,
    LOCAL_TEXT_2,
    LOCAL_TEXT_3,
    setLocalVariables,
    setTextOne,
    setTextThree,
    setTextTwo,
  ]);

  const router = useRouter();
  const time = new Date();
  time.setSeconds(time.getSeconds() + GLOBAL_EE_TEST_TIME);

  const submitTest = async (correctionType: string) => {
    setIsSubmitting(true);
    if (!(await checkCredit('TCF', 'EE'))) {
      setIsSubmitting(false);
      return;
    }

    const payloadValue = {
      textOne,
      textTwo,
      textThree,
    };

    sonner.promise(
      saveTestEEUseCase({
        payload: payloadValue,
        serie: serie._id,
        correctionType,
      }),
      {
        loading: 'Envoie du test en cours...',
        success: () => {
          update({
            remains: {
              remainTCF: {
                balance_ee: session?.user.remains?.remainTCF?.balance_ee! - 1,
                balance_eo: session?.user.remains?.remainTCF?.balance_eo,
                remain_day: session?.user.remains?.remainTCF?.remain_day,
              },
              remainTEF: session?.user.remains?.remainTEF,
            },
          });
          clearLocalStorage();
          resetEE();
          router.back();
          return "Vos productions écrites ont bien été soumises. Vous serrez informé(e) lorsqu'un résultat sera disponible";
        },
        error: (error) => {
          setIsSubmitting(false);
          return error.message === 'Failed to fetch'
            ? 'Verifier votre connexion'
            : error.message;
        },
        closeButton: true,
        duration: 1000 * 20,
      },
    );
  };

  const onExpire = async () => {
    setstarting(false);
    if (
      convert(textOne).trim().length > 0 ||
      convert(textTwo).trim().length > 0 ||
      convert(textThree).trim().length > 0
    ) {
      // Préparer les données pour la soumission
      setPendingSubmission({
        textOne,
        textTwo,
        textThree,
      });
      // Afficher le sélecteur de mode de correction
      setShowCorrectionSelector(true);
    } else {
      resetEE();
      clearLocalStorage();
      router.push(`/examen/tcf/${serie.libelle}`);
      sonner.warning('Veuillez écrire vos 3 productions avant de soumettre', {
        description: 'Vous pourrez terminer votre test plus tard',
        duration: 1000 * 20,
        closeButton: true,
      });
    }
  };

  const handleCorrectionModeConfirm = (correctionType: string) => {
    setShowCorrectionSelector(false);
    submitTest(correctionType);
  };

  const handleCorrectionModeCancel = () => {
    setShowCorrectionSelector(false);
    setPendingSubmission(null);
    setIsSubmitting(false);
  };

  return (
    <div className="relative flex w-full flex-col gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex w-full flex-col items-center justify-around border-b bg-white p-2 md:flex-row">
        <div className="flex items-center gap-px">
          <GlobalTimer
            key={TimeName}
            mode="EE"
            serieLib={serie.libelle}
            recordTime={true}
            expiryTimestamp={time}
            onExpire={() => onExpire()}
            starting={starting}
            totalduration={GLOBAL_EE_TEST_TIME}
          />
          <EndTextBtn onClick={() => onExpire()}>
            <button
              title="stop"
              disabled={isSubmitting}
              className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1.5 md:hidden md:p-3"
            >
              <p className="font-semibold text-white">Fin</p>
            </button>
          </EndTextBtn>
        </div>
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TCF: Objectif-Canada || Série {serie.libelle} || Expression écrite.
          </p>
        </div>
        <EndTextBtn onClick={() => onExpire()}>
          <button
            disabled={isSubmitting}
            title="stop"
            className="hidden aspect-video items-center justify-center rounded-sm bg-red-500 p-3 md:flex"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
      </div>

      <div className="mt-20 w-full md:mt-14">
        <Tabs defaultValue="task1" className="h-fit w-full">
          <TabsList className="!relative !gap-3">
            <TabsTrigger
              value="task1"
              className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
            >
              Tâche 1
            </TabsTrigger>
            <TabsTrigger
              value="task2"
              className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
            >
              Tâche 2
            </TabsTrigger>

            <TabsTrigger
              value="task3"
              className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
            >
              Tâche 3
            </TabsTrigger>
          </TabsList>
          <TabsContent value="task1">
            <Task num={1} text={textOne} task={taskOne} />
          </TabsContent>
          <TabsContent value="task2">
            <Task num={2} text={textTwo} task={taskTwo} />
          </TabsContent>
          <TabsContent value="task3">
            <Task num={3} text={textThree} task={taskThree} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Sélecteur de mode de correction */}
      <CorrectionModeSelector
        isOpen={showCorrectionSelector}
        onClose={handleCorrectionModeCancel}
        onConfirm={handleCorrectionModeConfirm}
        session={session!}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
