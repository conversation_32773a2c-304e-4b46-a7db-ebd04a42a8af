# Analyse technique pour migration mobile - objectifcanada-tcf.com

## 📋 Checklist d'analyse technique

### 🔐 Authentification & Rôles

**✅ Architecture actuelle identifiée :**
- **Méthode d'authentification :** JWT avec NextAuth.js
- **API d'authentification :** `/api/auth/login` (POST)
- **Récupération profil :** `/api/user/users/user-info` (GET)
- **Rôles supportés :**
  - `client` (utilisateur standard)
  - `admin` (administrateur)
  - `dealer` (revendeur)
  - `profil` (correcteur)
  - `superDealer` (super revendeur)

**🔧 Endpoints API identifiés :**
```
POST /api/auth/login
GET /api/user/users/user-info
```

### 📂 Ressources & Épreuves

**✅ Types de fichiers identifiés :**
- **Audio :** MP3, AAC (fichiers d'écoute pour CO et EO)
- **JSON :** Structure des questions/réponses
- **Images :** PNG, WEBP (illustrations des questions)
- **Texte :** Contenu des épreuves d'expression écrite

**📊 Formats de données :**
- **Questions TCF/TEF :** JSON avec suggestions multiples
- **Épreuves EE :** Texte libre avec contraintes (min/max mots)
- **Épreuves EO :** Fichiers audio uploadés par l'utilisateur

**🗂️ Stockage actuel :**
- **API de fichiers :** `${BASEURL}/api/question/file/`
- **Upload audio EO :** `${BASEURL}/api/user-eofile/upload`
- **Serveurs identifiés :**
  - Production : `tlf-api-backup.onrender.com`
  - Backup : `abjectof-conoda2.onrender.com`
  - Local : `localhost:3500`

**📏 Tailles de fichiers :**
- **Limite upload :** 5MB par fichier
- **Fichiers audio EO :** Variables (enregistrements utilisateur)
- **Images :** Optimisées avec cache 31 jours

### 🎯 Endpoints API des épreuves

**TCF :**
```
GET /api/serie/series/{libelle}?mode=COM|EE|EO
GET /api/eeTest/tests/user-info
GET /api/eoTest/tests/user-info
POST /api/user-eofile/upload
```

**TEF :**
```
GET /api/tef/serie/series/{libelle}?mode=COM|EE|EO
GET /api/tef/eeTest/tests/user-info
POST /api/user-eofile/upload
```

### 💳 Paiement & Abonnement

**✅ Systèmes de paiement identifiés :**
- **Stripe/Carte bancaire :** EUR (Europe/International)
- **Mobile Money :** XAF (Cameroun/Afrique)
  - MTN Mobile Money
  - Orange Money
- **PayPal :** Support identifié

**🔐 Vérification abonnement :**
- **Méthode :** JWT avec données utilisateur
- **Propriétés utilisateur :**
  - `solde` : Crédits restants
  - `remains` : Tests restants
  - `remainsDeals` : Tests dealer restants
  - `accountIsCheck` : Statut compte vérifié

**💰 Offres identifiées :**
- FREE, DECOUVERTE, APPROFONDIE, INTENSE, PERFECTION
- Prix variables selon pays (CMR, AFO, OTHER)

### 🎨 UI/UX & Fonctionnalités

**🌐 Langues :**
- **Principal :** Français (confirmé dans layout.tsx)
- **Support multi-langue :** Non implémenté actuellement

**📱 Design actuel :**
- Interface web responsive
- Composants réutilisables (Navbar, Footer)
- Thème sombre non implémenté

**⚡ Fonctionnalités spéciales :**
- **Mode hors-ligne :** Partiellement implémenté (séries gratuites)
- **Sauvegarde locale :** IndexedDB pour tests EO
- **Timer intégré :** Gestion des durées d'examen
- **Upload progressif :** Retry automatique pour fichiers audio

### 📱 Publication & Notifications

**🚀 Comptes développeurs :**
- [ ] Google Play Console (à confirmer)
- [ ] Apple Developer (à confirmer)

**🔔 Push notifications :**
- Non implémenté actuellement
- Potentiel : Rappels d'abonnement, nouvelles épreuves

---

## 🎯 Plan technique recommandé pour Expo

### 📱 Architecture mobile proposée

**🔧 Stack technique :**
- **Framework :** Expo (React Native)
- **Navigation :** React Navigation 6
- **État global :** Zustand ou Redux Toolkit
- **Authentification :** Expo SecureStore + JWT
- **Stockage local :** Expo SQLite + FileSystem
- **Audio :** Expo AV
- **Réseau :** Axios avec intercepteurs

### 🔐 Sécurité hors-ligne

**🛡️ Stratégies de protection :**
1. **Chiffrement local :** AES-256 pour fichiers téléchargés
2. **Vérification périodique :** Refresh token toutes les 24h
3. **Watermarking :** Identification utilisateur dans les fichiers
4. **Expiration automatique :** Suppression après fin d'abonnement

### 📦 Gestion hors-ligne

**💾 Stratégie de téléchargement :**
1. **Téléchargement sélectif :** Par série/discipline
2. **Compression :** Optimisation des fichiers audio
3. **Synchronisation :** Upload automatique des résultats
4. **Cache intelligent :** Gestion de l'espace disque

### 🚀 Workflow de développement

**📋 Étapes recommandées :**
1. **Phase 1 :** Authentification + Navigation
2. **Phase 2 :** Épreuves en ligne (CE/CO)
3. **Phase 3 :** Épreuves interactives (EE/EO)
4. **Phase 4 :** Mode hors-ligne sécurisé
5. **Phase 5 :** Paiements + Publication

---

## ❓ Questions restantes

### 🔍 Informations manquantes

1. **Taille moyenne des épreuves complètes ?**
2. **Fréquence de mise à jour du contenu ?**
3. **Nombre d'utilisateurs simultanés attendus ?**
4. **Budget alloué pour les stores (99$/an Apple + 25$ Google) ?**
5. **Délai souhaité pour la première version ?**

### 📊 Métriques à clarifier

- **Taille totale du contenu TCF/TEF**
- **Durée moyenne d'une session utilisateur**
- **Taux de conversion actuel web → abonnement**

---

## 🎯 Prochaines étapes

1. **Validation de cette analyse** avec votre équipe
2. **Création d'un prototype Expo** (authentification + navigation)
3. **Tests de performance** avec vrais fichiers audio
4. **Définition du MVP** (fonctionnalités prioritaires)
5. **Planning de développement** détaillé

---

*📝 Document généré automatiquement à partir de l'analyse du codebase Next.js*
*🔄 Dernière mise à jour : 23 août 2025*
