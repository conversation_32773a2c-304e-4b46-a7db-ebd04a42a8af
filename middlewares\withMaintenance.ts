import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';

export function withMaintenance(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const isMaintenance = process.env.MAINTENANCE_SITE;

    if (isMaintenance == 'true' && req.nextUrl.pathname !== '/maintenance') {
      return NextResponse.redirect(new URL(`/maintenance`, req.nextUrl));
    }
    return middleware(req, event);
  };
}
