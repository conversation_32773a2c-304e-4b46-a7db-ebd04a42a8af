'use client';

import { useState } from 'react';
import ImageZoomInOut from './ZoumInOutImage';
import logo from '@/public/logo4.png';
import { Button, buttonVariants } from './ui/button';
import wordCount from '@/lib/word-count';
import convert from '@/lib/helpers';
import { EEPayload, ResultatEE, Task } from '@/types';
import {
  getEEtext,
  getEEtextTEF,
  getMaxNote,
  getMaxNoteTEF,
} from '@/lib/utils';
import { BUCKET_BASE_URL } from '@/config';
import Link from 'next/link';
import parse from 'html-react-parser';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { ScrollArea } from './ui/scroll-area';
import { hasCommonDigit } from '@/app/examen/utils';

interface EECardTCFProps {
  payload: EEPayload;
  task: Task;
  detailSet: ResultatEE;
}
export function EECardTCF({ payload, task, detailSet }: EECardTCFProps) {
  const [showConsigne, setShowConsigne] = useState(true);
  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };
  const resultat = detailSet.resultat.find((res) =>
    hasCommonDigit(res.task, task.libelle.replaceAll(' ', '')),
  );
  const comment = resultat?.comment || '';

  const text = getEEtext(task.numero, payload);
  const max = getMaxNote(task.libelle);
  const moy = max / 2;
  const score = resultat?.note || 0;
  return (
    <div className="mb-3 grid gap-4 rounded-sm border-zinc-300 pt-4 md:border md:px-5">
      <div className="flex items-center justify-between">
        <div className="flex gap-5 font-medium">
          <h1 className="text-base capitalize underline underline-offset-4">
            {task.libelle}
          </h1>
          <span className="flex">
            <p
              className={`${
                score >= moy ? 'text-emerald-500' : 'text-red-500'
              }`}
            >
              {score}
            </p>
            /{max}
          </span>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant={'outline'} size={'sm'}>
              consigne
            </Button>
          </DialogTrigger>
          <DialogContent className="overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Consigne</DialogTitle>
              <DialogDescription className="max-h-[400px] text-left">
                <ScrollArea>
                  <div className="">
                    {showConsigne ? (
                      <>
                        <p>{task.minWord} mots mininmum</p>
                        <p>{task.maxWord} mots maximunm</p>
                        <div className="prose mt-5 max-w-[50ch] text-justify font-normal">
                          {parse(task.consigne)}
                        </div>
                      </>
                    ) : (
                      <>
                        {task.images?.map((img, i) => (
                          <ImageZoomInOut
                            imageUrl={
                              img.trim().length > 0
                                ? `${BUCKET_BASE_URL}/${img}`
                                : logo
                            }
                            placeholder={
                              img.trim().length > 0 ? 'empty' : 'blur'
                            }
                            key={i}
                          />
                        ))}
                      </>
                    )}
                    <div className="flex items-center gap-2">
                      {task.images?.length && task.images?.length > 0 ? (
                        <Button
                          className="mx-auto !max-w-[300px]"
                          onClick={handleEyes}
                        >
                          {showConsigne
                            ? 'Afficher les pieces jointes'
                            : 'Afficher la consigne'}
                        </Button>
                      ) : null}
                    </div>
                  </div>
                </ScrollArea>
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </div>
      <div className="flex h-fit flex-col items-start justify-between gap-5 p-4">
        <div className="order-2 flex flex-col gap-1">
          <div className="prose">{parse(comment)}</div>
        </div>
        <div className="bgc order-1 w-full">
          <div className="flex justify-end gap-5">
            <Link
              className={buttonVariants({ variant: 'link' })}
              href={`/correction/TCF/EE/${detailSet.serie.libelle}`}
            >
              Voir un exemple
            </Link>
          </div>
          <h1 className="mb-2 text-base font-bold">Votre réponse</h1>
          <div className="bg-size">
            <div className="bg-white/5 font-normal leading-7 backdrop-blur-[1px]">
              {parse(text)}
            </div>
          </div>
          <div className="mt-2 flex items-center gap-8">
            <span className="flex items-center gap-1 text-gray-400">
              Nombre de mots:{' '}
              <p className="font-semibold text-black">
                {wordCount(convert(text))}
              </p>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

interface EECardTEFProps {
  payload: EEPayload;
  section: Section;
  detailSet: any;
}
export function EECardTEF({ payload, section, detailSet }: EECardTEFProps) {
  const [showConsigne, setShowConsigne] = useState(true);
  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };
  const matchSection = section.libelle.replaceAll(' ', '').includes('A')
    ? 'section1'
    : 'section2';
  const resultat = detailSet.resultat.find((res: any) =>
    hasCommonDigit(res.section, matchSection),
  );
  const comment = resultat?.comment || '';

  const text = getEEtextTEF(section.numero, payload);
  const max = getMaxNoteTEF(section.libelle);
  const moy = max / 2;
  const score = resultat?.note || 0;
  return (
    <div className="mb-3 grid gap-4 rounded-sm border-zinc-300 pt-4 md:border md:px-5">
      <div className="flex items-center justify-between">
        <div className="flex gap-5 font-medium">
          <h1 className="text-base capitalize underline underline-offset-4">
            {section.libelle}
          </h1>
          <span className="flex">
            <p
              className={`${
                score >= moy ? 'text-emerald-500' : 'text-red-500'
              }`}
            >
              {score}
            </p>
            /{max}
          </span>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant={'outline'} size={'sm'}>
              consigne
            </Button>
          </DialogTrigger>
          <DialogContent className="overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Consigne</DialogTitle>
              <DialogDescription className="max-h-[400px] text-left">
                <ScrollArea>
                  <div className="">
                    {showConsigne ? (
                      <>
                        <p>{section.minWord} mots mininmum</p>
                        <p>{section.maxWord} mots maximunm</p>
                        <div className="prose mt-5 max-w-[50ch] text-justify font-normal">
                          {parse(section.consigne)}
                        </div>
                      </>
                    ) : (
                      <>
                        {section.images?.map((img, i) => (
                          <ImageZoomInOut
                            imageUrl={
                              img.trim().length > 0
                                ? `${BUCKET_BASE_URL}/${img}`
                                : logo
                            }
                            placeholder={
                              img.trim().length > 0 ? 'empty' : 'blur'
                            }
                            key={i}
                          />
                        ))}
                      </>
                    )}
                    <div className="flex items-center gap-2">
                      {section.images?.length && section.images?.length > 0 ? (
                        <Button
                          className="mx-auto !max-w-[300px]"
                          onClick={handleEyes}
                        >
                          {showConsigne
                            ? 'Afficher les pieces jointes'
                            : 'Afficher la consigne'}
                        </Button>
                      ) : null}
                    </div>
                  </div>
                </ScrollArea>
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </div>
      <div className="flex h-fit flex-col items-start justify-between gap-5 p-4">
        <div className="order-2 flex flex-col gap-1">
          <div className="prose">{parse(comment)}</div>
        </div>
        <div className="bgc order-1 w-full">
          <div className="flex justify-end gap-5">
            <Link
              className={buttonVariants({ variant: 'link' })}
              href={`/correction/TEF/EE/${detailSet.serie.libelle}`}
            >
              Voir un exemple
            </Link>
          </div>
          <h1 className="mb-2 text-base font-bold">Votre réponse</h1>
          <div className="bg-size">
            <div className="bg-white/5 font-normal leading-7 backdrop-blur-[1px]">
              {parse(text)}
            </div>
          </div>
          <div className="mt-2 flex items-center gap-8">
            <span className="flex items-center gap-1 text-gray-400">
              Nombre de mots:{' '}
              <p className="font-semibold text-black">
                {wordCount(convert(text))}
              </p>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
