import { fechSerie } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { isTestAvailable } from '@/lib/utils';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import TestEETCF from '../../../_components/testEE';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  if (!session)
    redirect(`/auth?callbackUrl=/examen/tcf/${slug}/expression-ecrite`);
  const serie = await fechSerie(slug, session.user.accessToken, 'EE');
  if (!serie) return notFound();

  if (!isTestAvailable('EE', serie)) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="mt-5 w-full">
      <TestEETCF serie={serie} />
    </section>
  );
}

export default Page;
