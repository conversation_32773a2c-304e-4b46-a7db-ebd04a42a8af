import { getAuthSession } from '@/lib/auth';
import { getCanUse } from '@/lib/utils';
import React from 'react';
import { MethodologieBtn, SerieBtn } from '../_components/button';
import { getFreeSerrie } from '@/lib/free-serie-helper';
import { getSeriesLibelles } from '../actions';
async function Page() {
  const session = await getAuthSession();
  const series = await getSeriesLibelles(
    'TCF',
    session?.user.accessToken || '',
  );

  const CAN_USE = getCanUse(session, 'TCF');
  const FREE_TCF_LIB = await getFreeSerrie('tcf');

  const FREE_LIB = series.filter(
    (s) => FREE_TCF_LIB?.includes(s.libelle) && Number(s.libelle) <= 999,
  );
  const PAY_LIB = series.filter(
    (s) => !FREE_TCF_LIB?.includes(s.libelle) && Number(s.libelle) <= 999,
  );

  return (
    <section className="mt-4 flex h-fit flex-col items-center justify-start gap-10 md:container md:p-0">
      <h1 className="text-xl font-bold text-black">
        {' '}
        {CAN_USE ? 'Continuez votre préparation' : ' Débutez la simulation !'}
      </h1>
      <p className="prose">
        Vous êtes proche de votre objectif ! Choisissez une série, ensuite
        sélectionnez la discipline et commencez à vous exercer.
      </p>
      <div className="mb-4 grid w-full max-w-screen-md grid-cols-1 gap-2 lg:grid-cols-2 lg:gap-4">
        {FREE_LIB.map((s) => {
          return (
            <SerieBtn
              type={'tcf'}
              CanUse={true}
              lib={s.libelle}
              key={s.libelle}
              session={s.session}
            />
          );
        })}
        <MethodologieBtn type={'TCF'} CanUse={CAN_USE} />
        {PAY_LIB.map((s) => {
          return (
            <SerieBtn
              type={'tcf'}
              CanUse={CAN_USE}
              lib={s.libelle}
              key={s.libelle}
              session={s.session}
            />
          );
        })}
      </div>
    </section>
  );
}

export default Page;
