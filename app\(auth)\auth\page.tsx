'use client';
import Image from 'next/image';
import Link from 'next/link';
import React, { Suspense } from 'react';
import logo from '@/public/logo4.png';
import logo2 from '@/public/auth.webp';
import AuthButtons from './content';
function AuthPage() {
  return (
    <div className="mb-20 block h-full flex-col items-center justify-center overflow-hidden bg-white md:container md:grid md:h-[400px] md:rounded-md md:border md:px-4 md:py-10 md:shadow-md lg:max-w-none lg:grid-cols-2 lg:px-0 lg:py-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0">
          <Image
            fill
            alt="bg"
            src={logo2}
            placeholder="blur"
            className="object-cover"
          />
        </div>
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          OBJECTIF CANADA
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2"></blockquote>
        </div>
      </div>
      <div className="flex flex-col items-center">
        <Link href={'/'} className="hidden w-fit drop-shadow-md md:flex">
          <div className="mb-3 overflow-hidden rounded-full border">
            <Image
              alt="logo"
              priority
              className="h-[120px] w-[120px]"
              src={logo}
            />
          </div>
        </Link>
        <div className="flex items-center justify-center gap-3">
          <Suspense>
            <AuthButtons />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

export default AuthPage;
