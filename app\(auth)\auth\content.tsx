'use client';
import { buttonVariants } from '@/components/ui/button';
import Link from 'next/link';
import { useQueryState } from 'nuqs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

function AuthButtons() {
  const [callback] = useQueryState('callbackUrl');
  const [expired] = useQueryState('expired');

  return (
    <div className="flex flex-col items-center gap-4">
      {expired === 'true' && (
        <Alert className="w-full max-w-md border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            Votre session a expiré. Veuillez vous reconnecter pour continuer.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex gap-3">
        <Link
          href={`/signup?callbackUrl=${encodeURIComponent(callback ?? '/')}`}
          className={buttonVariants({ variant: 'default' })}
          replace={true}
        >
          Créer un compte
        </Link>
        <Link
          href={`/signin?callbackUrl=${encodeURIComponent(callback ?? '/')}`}
          className={buttonVariants({
            variant: 'ghost',
            className: 'border',
          })}
          replace={true}
        >
          Se connecter
        </Link>
      </div>
    </div>
  );
}

export default AuthButtons;
