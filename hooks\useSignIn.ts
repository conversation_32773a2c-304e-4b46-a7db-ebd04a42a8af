'use client';

import { signIn, getSession } from 'next-auth/react';
import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import logger from '@/lib/logger';

interface SignInData {
  email: string;
  password: string;
}

interface UseSignInReturn {
  signInUser: (data: SignInData, callbackUrl?: string) => Promise<void>;
  isLoading: boolean;
}

export function useSignIn(): UseSignInReturn {
  const [isLoading, setIsLoading] = useState(false);

  const signInUser = async (data: SignInData, callbackUrl?: string) => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email: data.email.trim(),
        password: data.password,
        redirect: false,
        callbackUrl: callbackUrl || '/',
      });

      if (result?.error) {
        toast({
          title: 'Erreur de connexion',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      if (result?.ok) {
        toast({
          title: 'Connexion réussie',
          description: 'Bienvenue !',
        });

        // Attendre plus longtemps pour que NextAuth synchronise complètement le token
        // côté serveur (cookies, JWT, etc.)
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Récupérer la session mise à jour pour vérifier le rôle
        let updatedSession = await getSession();

        // Vérification supplémentaire : s'assurer que la session est bien mise à jour
        let retries = 0;
        while (!updatedSession && retries < 3) {
          logger.warn(
            `Session not updated after sign in, retry ${retries + 1}/3`,
          );
          await new Promise((resolve) => setTimeout(resolve, 1000));
          updatedSession = await getSession();
          retries++;
        }

        // Vérifier que le token est valide côté serveur avant de rediriger
        if (updatedSession?.user?.accessToken) {
          try {
            const tokenValidation = await fetch('/api/auth/validate-token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                token: updatedSession.user.accessToken,
              }),
            });

            if (!tokenValidation.ok) {
              logger.warn('Token not yet valid on server, waiting...');
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          } catch (error) {
            logger.warn('Could not validate token, proceeding anyway', {
              error: String(error),
            });
          }
        }

        // Déterminer l'URL de redirection basée sur le rôle
        let redirectUrl: string;
        if (updatedSession?.user?.role === 'superdealer') {
          redirectUrl = '/dashboard/superdealer-groups';
          logger.info('Redirecting superdealer to dashboard', {
            userId: updatedSession.user._id,
          });
        } else {
          redirectUrl = callbackUrl || '/';
        }

        // Utiliser window.location.href pour forcer un rechargement complet
        // Cela permet au middleware de récupérer le nouveau token
        logger.info('Redirecting after successful sign in', { redirectUrl });
        window.location.href = redirectUrl;
      }
    } catch (error) {
      logger.error('Erreur lors de la connexion:', error);
      toast({
        title: 'Erreur',
        description: "Une erreur inattendue s'est produite",
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signInUser,
    isLoading,
  };
}
