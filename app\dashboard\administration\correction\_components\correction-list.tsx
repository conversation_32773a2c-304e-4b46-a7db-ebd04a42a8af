import { ExpressionTest, ExpressionTestResult } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';
import ExpressionList from './tables';
import logger from '@/lib/logger';
import ErrorCard from '@/components/error-card';
type ExpressionTestResultWithExamen = ExpressionTestResult & {
  examen: string;
};
const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
async function getTests(token: string, mode: string, examen: string) {
  try {
    const { data } = await axios.get<any>(
      `${baseUrl}/api/${examen == 'TCF' ? '' : 'tef/'}${mode == 'EE' ? 'eeTest/tests' : 'eoTest/tests'}${examen == 'TCF' ? '/user' : ''}/pending`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    let result: ExpressionTestResultWithExamen[] = [];
    if (examen == 'TEF') {
      result = data.map((test: ExpressionTest) => ({
        ...test,
        type: mode as any,
        examen: examen.toLowerCase() as any,
      }));
    } else {
      result = data.tests.map((test: ExpressionTest) => ({
        ...test,
        type: mode as any,
        examen: examen.toLowerCase() as any,
      }));
    }

    return {
      success: true,
      data: result.filter((test: ExpressionTestResultWithExamen) => test.user),
    };
  } catch (error) {
    logger.error('Erreur lors de la récupération des tests:', error);
    if (axios.isAxiosError(error)) {
      if (error.response?.status == 401) {
        return { success: false, error: 'Session expirée' };
      } else if (error.response?.status == 404) {
        return { success: true, data: [] };
      }
    } else if (error instanceof Error) {
      if (error.message === 'Network Error') {
        return { success: false, error: 'Verifier votre connexion internet' };
      }
    }
    return { success: false, error: 'Erreur inconnue' };
  }
}

export const CorrectionList = async ({
  mode,
  examen,
}: {
  examen: string;
  mode: string;
}) => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const result = await getTests(token, mode, examen);

  if (!result.success) {
    return <ErrorCard error={result.error!} />;
  }
  return <ExpressionList data={result.data!} />;
};

export const ExpressionLoader = () => {
  return <Skeleton className="h-[100px] w-full rounded-md" />;
};
