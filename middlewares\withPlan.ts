import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';

import { getDayCount } from './helpers';

export function withPlan(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const path = req.nextUrl.pathname;

    const callBackUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`;
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (path.includes('/examen/tcf') && path !== '/examen/tcf') {
      const days =
        token?.role == 'admin'
          ? 100
          : getDayCount(token?.remains?.remainTCF?.remain_day || null);

      if (days <= 0) {
        return NextResponse.redirect(
          new URL(
            `/checkout?callbackUrl=${encodeURIComponent(callBackUrl + '&next=/')}`,
            req.nextUrl,
          ),
        );
      }
    }

    if (path.includes('/examen/tef') && path !== '/examen/tef') {
      const days =
        token?.role == 'admin'
          ? 100
          : getDayCount(token?.remains?.remainTEF?.remain_day || null);

      if (days <= 0) {
        return NextResponse.redirect(
          new URL(
            `/checkout?callbackUrl=${encodeURIComponent(callBackUrl + '&next=/')}`,
            req.nextUrl,
          ),
        );
      }
    }
    if (path.includes('/renew-expression-credits/client')) {
      const TEF_Day =
        token?.role == 'admin'
          ? 100
          : getDayCount(token?.remains?.remainTEF?.remain_day || null);

      const TCF_Day =
        token?.role == 'admin'
          ? 100
          : getDayCount(token?.remains?.remainTCF?.remain_day || null);

      if (TCF_Day <= 0 && TEF_Day <= 0) {
        return NextResponse.redirect(
          new URL(
            `/checkout?callbackUrl=${encodeURIComponent(callBackUrl + '&next=/')}`,
            req.nextUrl,
          ),
        );
      }
    }

    if (path.includes('/renew-expression-credits/dealer')) {
      const dealer_Day =
        token?.role == 'admin'
          ? 100
          : getDayCount(token?.remainsDeals?.remainTCF?.remain_day || null);

      if (dealer_Day <= 0) {
        return NextResponse.redirect(
          new URL(
            `/checkout-dealer?callbackUrl=${encodeURIComponent(callBackUrl + '&next=/')}`,
            req.nextUrl,
          ),
        );
      }
    }

    return middleware(req, event);
  };
}
