const BASEURL =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
const BASEURL_V1 = 'https://tpc-services-huga.onrender.com';
const WHATSAPP_LINK = 'https://chat.whatsapp.com/IxJGWCngOAK88zXAsEn67w';
const FB_LINK = 'https://www.facebook.com/profile.php?id=61552486020448';
const SITE_URL = 'https://objectifcanada-tcf.com';
const FREE_TCF_LIB = '150';
const FREE_TEF_LIB = '1002';
const ROUTES = [
  {
    path: 'news/tef',
    value: "Sujets d'actualité TEF",
  },
  {
    path: 'news/tcf',
    value: "Sujets d'actualité TCF",
  },
  {
    path: 'about',
    value: 'A Propos du TCF',
  },
  {
    path: 'contact',
    value: 'Contact',
  },
  {
    path: 'politique-confidentialite',
    value: 'Politique de confidentialite',
  },
  {
    path: 'condition-remboursement',
    value: 'Condition de remboursement',
  },
  {
    path: 'faq',
    value: 'FAQ',
  },
] as const;

const base =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';

const GLOBAL_CE_TEST_TIME = 60 * 60;
const GLOBAL_CO_TEST_TIME = 60 * 40;
const GLOBAL_EE_TEST_TIME = 60 * 60;
const EO_TASK_ONE_TIME = 60 * 2;
const EO_TASK_TWO_TIME = 60 * 2.5;
const EO_TASK_THREE_TIME = 60 * 4.5;
const PREPARATION_TIME = 60 * 2;
const BUCKET_BASE_URL = `${base}/api/question/file`;
const BUCKET_BASE_EO_URL = `${base}/api/user-eofile/file`;
const EO_SECTION_A_TIME = 60 * 5;
const EO_SECTION_B_TIME = 60 * 10;
const EO_SECTION_A_FILENAME = 'SECTION A NEW.aac';
const EO_SECTION_B_FILENAME = 'SECTION B NEW.aac';
const EO_T1_FILENAME = 'T1.mp3';
const EO_T2_FILENAME = 'T2.mp3';
const EO_T3_FILENAME = 'T3.mp3';
const PAYMENT_KEYS = {
  TCF: 'subscription_pack_tcf',
  TEF: 'subscription_pack_tef',
  DEALER_TEF: 'subscription_pack_deal_tef',
  DEALER_TCF: 'subscription_pack_deal_tcf',
  EXPRESSION_TEF: 'subscription_only_ee_eo_tef',
  EXPRESSION_TCF: 'subscription_only_ee_eo_tcf',
  DEALER_EXPRESSION: 'subscription_only_ee_eo_dealer_tcf',
} as const;

const TEST_URL_ON_TEST = new RegExp(
  /^\/examen\/(tcf|tef)\/(10000|[1-9][0-9]{0,4}|[1-9])\/(.+)$/,
);

const CHECKOUTSTEPS = [1, 2, 3, 4];
const EXAMS = ['TCF', 'TEF'] as const;
const PACKS = [
  'FREE',
  'APPROFONDIE',
  'INTENSE',
  'DECOUVERTE',
  'PERFECTION',
] as const;
const METHODS = ['CARD', 'PAYPAL', 'MTN', 'ORANGE'] as const;
export const CHECKOUTDEALERSETP = [1, 2, 3];
export {
  BASEURL,
  ROUTES,
  GLOBAL_CE_TEST_TIME,
  GLOBAL_CO_TEST_TIME,
  GLOBAL_EE_TEST_TIME,
  EO_TASK_ONE_TIME,
  EO_TASK_TWO_TIME,
  EO_TASK_THREE_TIME,
  PREPARATION_TIME,
  BUCKET_BASE_URL,
  BASEURL_V1,
  BUCKET_BASE_EO_URL,
  FB_LINK,
  WHATSAPP_LINK,
  SITE_URL,
  FREE_TCF_LIB,
  FREE_TEF_LIB,
  EO_SECTION_A_TIME,
  EO_SECTION_B_TIME,
  EO_SECTION_A_FILENAME,
  EO_SECTION_B_FILENAME,
  PAYMENT_KEYS,
  TEST_URL_ON_TEST,
  CHECKOUTSTEPS,
  EXAMS,
  PACKS,
  METHODS,
  EO_T1_FILENAME,
  EO_T2_FILENAME,
  EO_T3_FILENAME,
};
