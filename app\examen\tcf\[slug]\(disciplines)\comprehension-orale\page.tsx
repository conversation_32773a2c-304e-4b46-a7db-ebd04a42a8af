import { fechSerie } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { isTestAvailable } from '@/lib/utils';
import { notFound } from 'next/navigation';
import React from 'react';
import TestCOTCF from '../../../_components/testCO';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  const serie = await fechSerie(slug, session?.user.accessToken, 'COM');
  if (!serie) return notFound();

  if (!isTestAvailable('CO', serie)) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="w-full">
      <TestCOTCF serie={serie} />
    </section>
  );
}

export default Page;
