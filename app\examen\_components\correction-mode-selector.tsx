'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { isProfile } from '@/lib/utils';
import type { Session } from 'next-auth';

interface CorrectionModeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (correctionType: string) => void;
  session: Session;
  isSubmitting: boolean;
}

export default function CorrectionModeSelector({
  isOpen,
  onClose,
  onConfirm,
  session,
  isSubmitting,
}: CorrectionModeSelectorProps) {
  const [selectedMode, setSelectedMode] = useState<string>('');

  const isProfileUser = isProfile(session?.user?.email);

  // Définir les options selon le type d'utilisateur
  const getCorrectionOptions = () => {
    if (isProfileUser) {
      // Les profils n'ont que l'option humain
      return [
        {
          value: 'humain',
          label: 'Humain',
          description: 'Correction par un correcteur humain (plus précise)',
        },
      ];
    } else {
      return [
        {
          value: 'ia_renforce',
          label: 'Humain',
          description: 'Correction par un correcteur humain (plus précise)',
        },
        {
          value: 'ia',
          label: 'IA',
          description: 'Correction instantanée par IA',
        },
      ];
    }
  };

  const options = getCorrectionOptions();

  // Pour les profils, sélectionner automatiquement "humain"
  React.useEffect(() => {
    if (isProfileUser && isOpen) {
      setSelectedMode('humain');
    }
  }, [isProfileUser, isOpen]);

  const handleConfirm = () => {
    if (selectedMode) {
      onConfirm(selectedMode);
    }
  };

  const handleClose = () => {
    setSelectedMode('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Choisir le mode de correction</DialogTitle>
          <DialogDescription>
            Sélectionnez le type de correction que vous souhaitez pour votre
            test.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <RadioGroup value={selectedMode} onValueChange={setSelectedMode}>
            {options.map((option) => (
              <div
                key={option.value}
                className="flex items-start space-x-3 space-y-0"
              >
                <RadioGroupItem
                  value={option.value}
                  id={option.value}
                  className="mt-1"
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor={option.value}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedMode || isSubmitting}
          >
            {isSubmitting ? 'Envoi en cours...' : 'Confirmer'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
