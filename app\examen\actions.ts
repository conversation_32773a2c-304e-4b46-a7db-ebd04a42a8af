import { getFree<PERSON><PERSON>rie } from '@/lib/free-serie-helper';
import { Serie, LibelleSerie } from '@/types';
import axios, { AxiosError } from 'axios';
import logger from '@/lib/logger';

const base =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
export const fechSerie = async (
  libele: string,
  token?: string,
  mode?: 'COM' | 'EE' | 'EO',
) => {
  try {
    const FREE_TCF_LIB = await getFreeSerrie('tcf');

    const path = FREE_TCF_LIB?.includes(libele)
      ? `/offline/${libele}`
      : `/${libele}`;
    const { data } = await axios.get<Serie>(
      `${base}/api/serie/series${path}${mode ? `?mode=${mode}` : ''}`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
    return data;
  } catch (error) {
    logger.error('Error fetching serie', error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        return null;
      }
      throw new Error(error.response?.data.message);
    }
    throw new Error("Une erreur s'est produite", { cause: error });
  }
};

export const verifSerie = async (
  libele: string,
  token?: string,
): Promise<boolean> => {
  try {
    const { data } = await axios.get<any>(
      `${base}/api/serie/series/checkLibelle/${libele}`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
    return data.serieExist as boolean;
  } catch (error) {
    logger.error('Error verifying serie', error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        return false;
      }
      throw new Error(error.response?.data.message);
    }
    throw new Error("Une erreur s'est produite", { cause: error });
  }
};

export const fechSerieTEF = async (
  libele: string,
  token?: string,
  mode?: 'COM' | 'EE' | 'EO',
) => {
  try {
    const FREE_TEF_LIB = await getFreeSerrie('tef');

    const path = FREE_TEF_LIB?.includes(libele)
      ? `/offline/${libele}`
      : `/${libele}`;
    const { data } = await axios.get<SerieTEF>(
      `${base}/api/tef/serie/series${path}${mode ? `?mode=${mode}` : ''}`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
    return data;
  } catch (error) {
    logger.error('Error fetching TEF serie', error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        return null;
      }
    }
    throw new Error("Une erreur s'est produite", { cause: error });
  }
};

export const fetchCETEF = async (libele: string, token?: string) => {
  try {
    const { data } = await axios.get<Questionnaire>(
      `${base}/api/tef/serie/series/ce-consignes/${libele}`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
    return data;
  } catch (error) {
    logger.error('Error fetching CE TEF', error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        return null;
      }
    }
    throw new Error("Une erreur s'est produite", { cause: error });
  }
};
export const getSeriesLibelles = async (
  type: 'TCF' | 'TEF',
  token?: string,
) => {
  try {
    const { data } = await axios.get<LibelleSerie[]>(
      `${base}/api${type === 'TEF' ? '/tef' : ''}/serie/series/libelles`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
    return data
      .filter((s) => s.libelle !== '')
      .sort((a, b) => {
        return parseInt(a.libelle) - parseInt(b.libelle);
      });
  } catch (error) {
    logger.error('Error fetching series libelles', error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        return [];
      }
      throw new Error(error.response?.data.message);
    }
    throw new Error("Une erreur s'est produite", { cause: error });
  }
};
