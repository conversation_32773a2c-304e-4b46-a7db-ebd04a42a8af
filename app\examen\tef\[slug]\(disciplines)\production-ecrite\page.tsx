import { fechSerieTEF } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import TestEETEF from '../../../_components/testEE';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  if (!session)
    redirect(`/auth?callbackUrl=/examen/tef/${slug}/production-ecrite`);
  const serie = await fechSerieTEF(slug, session.user.accessToken, 'EE');
  if (!serie) return notFound();

  if (
    serie.eeQuestions.length <= 0 ||
    serie.eeQuestions[0].sections.length < 2
  ) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="mt-5 w-full">
      <TestEETEF serie={serie} />
    </section>
  );
}

export default Page;
