import { ZodSchema, z } from 'zod';
import API from './axios';
import { CombinedType } from '@/types';
import { NetworkError } from '@/types/error';
import { AxiosError } from 'axios';
import logger from './logger';

// Types pour une meilleure structure
type PaymentMethod = {
  name: string;
  url: string;
  schemaData: ZodSchema;
};

type PaymentResult = {
  data: any;
  success: boolean;
  transactionRef?: string;
};

type PaymentError = {
  message: string;
  code?: string;
  aggregator: string;
  attempt: number;
};

class Aggregator {
  name: string;
  paymentMethods: PaymentMethod[];

  constructor(name: string, paymentMethods: PaymentMethod[]) {
    this.name = name;
    this.paymentMethods = paymentMethods;
  }

  async initializePayment(
    paymentMethod: PaymentMethod,
    dto: CombinedType,
    path: string,
  ): Promise<PaymentResult> {
    // Préparer les données avec validation
    const paymentData = { ...dto, lang: 'fr' };

    try {
      logger.info('PaymentData', paymentData);
      const payload = paymentMethod.schemaData.safeParse(paymentData);
      if (!payload.success) {
        const prettyError = z.prettifyError(payload.error);
        logger.error('Validation error:', prettyError);
        throw new Error(prettyError);
      }

      const response = await API.post(paymentMethod.url, payload.data, {
        headers: {
          'Current-Path': path,
        },
        timeout: 30000, // 30 secondes de timeout
      });
      return {
        data: response.data,
        success: true,
        transactionRef: response.data?.transaction_ref,
      };
    } catch (error) {
      // Log structuré pour le debugging
      logger.error(`[${this.name}] Payment initialization failed:`, {
        method: paymentMethod.name,
        path,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }
}

// Schémas de validation Zod avec validation plus stricte
const mycoolPayMobilSchema = z.object({
  amount: z.number().positive('Le montant doit être positif'),
  name: z.string().min(1, 'Le nom est requis'),
  email: z.string().email('Email invalide'),
  currency: z.string().min(3, 'Code devise invalide'),
  reference: z.string().min(1, 'Référence requise'),
  lang: z.string().default('fr'),
  reason: z.string().min(1, 'Raison requise'),
  phone: z.number().int('Numéro de téléphone invalide'),
  offre: z.string().min(1, 'Offre requise'),
  examen: z.string().min(1, 'Examen requis'),
  channel: z.string().min(1, 'Canal requis'),
  parrain: z.string().optional(),
});

const mycoolPayMobilMethod: PaymentMethod = {
  name: 'mobile',
  url: '/api/coolPayments/initialize',
  schemaData: mycoolPayMobilSchema,
};

// Liste des agrégateurs
export const AGGREGATORS = [
  new Aggregator('MycoolPay', [mycoolPayMobilMethod]),
];

// Configuration des tentatives
const PAYMENT_CONFIG = {
  MAX_RETRIES_PER_AGGREGATOR: 2,
  RETRY_DELAY_MS: 1000,
} as const;

// Fonction utilitaire pour attendre
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Fonction pour initialiser un paiement avec retry et fallback
export async function initGeneralPayment(
  paymentMethodName: string,
  dto: CombinedType,
  aggregators: Aggregator[],
  path: string,
): Promise<any> {
  const errors: PaymentError[] = [];

  // Parcourir tous les agrégateurs
  for (
    let aggregatorIndex = 0;
    aggregatorIndex < aggregators.length;
    aggregatorIndex++
  ) {
    const currentAggregator = aggregators[aggregatorIndex];

    // Trouver la méthode de paiement (correction du bug critique)
    const paymentMethod = currentAggregator.paymentMethods.find(
      (pm: PaymentMethod) => pm.name === paymentMethodName,
    );

    if (!paymentMethod) {
      logger.warn(
        `[${currentAggregator.name}] Payment method '${paymentMethodName}' not found`,
      );
      continue;
    }

    // Tentatives avec retry pour cet agrégateur
    for (
      let attempt = 1;
      attempt <= PAYMENT_CONFIG.MAX_RETRIES_PER_AGGREGATOR;
      attempt++
    ) {
      try {
        logger.info(
          `[${currentAggregator.name}] Attempt ${attempt}/${PAYMENT_CONFIG.MAX_RETRIES_PER_AGGREGATOR}`,
        );

        const paymentResult = await currentAggregator.initializePayment(
          paymentMethod,
          dto,
          path,
        );

        if (paymentResult.success) {
          logger.info(
            `[${currentAggregator.name}] Payment initialized successfully`,
          );
          return paymentResult.data;
        }
      } catch (error) {
        const errorInfo: PaymentError = {
          message:
            error instanceof AxiosError
              ? error.response?.data?.message || 'Unknown error'
              : 'Unknown error',
          code: (error as any)?.code,
          aggregator: currentAggregator.name,
          attempt,
        };

        errors.push(errorInfo);

        // Gestion spécifique des erreurs réseau
        if (error instanceof NetworkError) {
          throw new Error('Verifier votre connexion internet');
        }

        logger.error(
          `[${currentAggregator.name}] Attempt ${attempt} failed:`,
          errorInfo,
        );

        // Attendre avant la prochaine tentative (sauf pour la dernière)
        if (attempt < PAYMENT_CONFIG.MAX_RETRIES_PER_AGGREGATOR) {
          await delay(PAYMENT_CONFIG.RETRY_DELAY_MS);
        }
      }
    }

    logger.warn(
      `[${currentAggregator.name}] All attempts failed, trying next aggregator`,
    );
  }

  // Tous les agrégateurs ont échoué
  logger.error('All payment aggregators failed:', errors);

  // Afficher un message d'erreur plus informatif
  const lastError = errors[errors.length - 1];
  const errorMessage = lastError?.message || 'Erreur inconnue';

  throw new Error(`Impossible d'initier le paiement: ${errorMessage}`);
}

// Fonction utilitaire pour valider les données de paiement
export function validatePaymentData(
  paymentMethodName: string,
  dto: CombinedType,
  aggregators: Aggregator[] = AGGREGATORS,
): {
  isValid: boolean;
  errors: string[];
  aggregator?: Aggregator;
  method?: PaymentMethod;
} {
  const errors: string[] = [];

  // Vérifier si des agrégateurs sont disponibles
  if (!aggregators || aggregators.length === 0) {
    errors.push('Aucun agrégateur de paiement configuré');
    return { isValid: false, errors };
  }

  // Trouver l'agrégateur et la méthode
  let foundAggregator: Aggregator | undefined;
  let foundMethod: PaymentMethod | undefined;

  for (const aggregator of aggregators) {
    const method = aggregator.paymentMethods.find(
      (pm) => pm.name === paymentMethodName,
    );
    if (method) {
      foundAggregator = aggregator;
      foundMethod = method;
      break;
    }
  }

  if (!foundAggregator || !foundMethod) {
    errors.push(`Méthode de paiement '${paymentMethodName}' non trouvée`);
    return { isValid: false, errors };
  }

  // Valider les données avec le schéma

  const result = foundMethod.schemaData.safeParse({ ...dto, lang: 'fr' });
  if (!result.success) errors.push(z.prettifyError(result.error));

  return {
    isValid: errors.length === 0,
    errors,
    aggregator: foundAggregator,
    method: foundMethod,
  };
}

// Fonction pour obtenir les méthodes de paiement disponibles
export function getAvailablePaymentMethods(
  aggregators: Aggregator[] = AGGREGATORS,
): string[] {
  return aggregators.flatMap((aggregator) =>
    aggregator.paymentMethods.map((method) => method.name),
  );
}

// Fonction pour obtenir les informations d'un agrégateur
export function getAggregatorInfo(
  aggregatorName: string,
  aggregators: Aggregator[] = AGGREGATORS,
) {
  return aggregators.find((agg) => agg.name === aggregatorName);
}
