import { BASEURL } from '@/config';
import logger from '@/lib/logger';

export const getFreeSerrie = async (type: 'tef' | 'tcf') => {
  try {
    const response = await fetch(`${BASEURL}/api/free_serie/${type}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
      },
    });

    if (!response.ok) {
      logger.error(`Error fetching free series for ${type}`, {
        status: response.status,
        statusText: response.statusText,
      });

      if (response.status === 400 || response.status === 404) {
        return null;
      }

      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    logger.error('Error in getFreeSerrie', error);

    // Gestion des erreurs de réseau ou de parsing JSON
    if (error instanceof TypeError && error.message.includes('fetch')) {
      logger.error('Network error in getFreeSerrie');
      return null;
    }

    if (error instanceof SyntaxError) {
      logger.error('JSON parsing error in getFreeSerrie');
      return null;
    }

    // Re-throw les autres erreurs
    throw error;
  }
};
